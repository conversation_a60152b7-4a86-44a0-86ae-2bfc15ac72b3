!function(n,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(n="undefined"!=typeof globalThis?globalThis:n||self).StyleToObject=r()}(this,(function(){"use strict";function n(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var r,t;var e=n(function(){if(t)return r;t=1;var n=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,e=/\n/g,o=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,u=/^:\s*/,c=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,f=/^[;\s]*/,a=/^\s+|\s+$/g,s="";function l(n){return n?n.replace(a,s):s}return r=function(r,t){if("string"!=typeof r)throw new TypeError("First argument must be a string");if(!r)return[];t=t||{};var a=1,p=1;function h(n){var r=n.match(e);r&&(a+=r.length);var t=n.lastIndexOf("\n");p=~t?n.length-t:p+n.length}function v(){var n={line:a,column:p};return function(r){return r.position=new d(n),g(),r}}function d(n){this.start=n,this.end={line:a,column:p},this.source=t.source}function m(n){var e=new Error(t.source+":"+a+":"+p+": "+n);if(e.reason=n,e.filename=t.source,e.line=a,e.column=p,e.source=r,!t.silent)throw e}function y(n){var t=n.exec(r);if(t){var e=t[0];return h(e),r=r.slice(e.length),t}}function g(){y(o)}function w(n){var r;for(n=n||[];r=b();)!1!==r&&n.push(r);return n}function b(){var n=v();if("/"==r.charAt(0)&&"*"==r.charAt(1)){for(var t=2;s!=r.charAt(t)&&("*"!=r.charAt(t)||"/"!=r.charAt(t+1));)++t;if(t+=2,s===r.charAt(t-1))return m("End of comment missing");var e=r.slice(2,t-2);return p+=2,h(e),r=r.slice(t),p+=2,n({type:"comment",comment:e})}}function A(){var r=v(),t=y(i);if(t){if(b(),!y(u))return m("property missing ':'");var e=y(c),o=r({type:"declaration",property:l(t[0].replace(n,s)),value:e?l(e[0].replace(n,s)):s});return y(f),o}}return d.prototype.content=r,g(),function(){var n,r=[];for(w(r);n=A();)!1!==n&&(r.push(n),w(r));return r}()}}());return function(n,r){var t=null;if(!n||"string"!=typeof n)return t;var o=e(n),i="function"==typeof r;return o.forEach((function(n){if("declaration"===n.type){var e=n.property,o=n.value;i?r(e,o,n):o&&((t=t||{})[e]=o)}})),t}}));
//# sourceMappingURL=style-to-object.min.js.map
