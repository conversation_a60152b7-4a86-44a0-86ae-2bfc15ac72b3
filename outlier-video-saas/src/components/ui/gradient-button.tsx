import { forwardRef } from "react";
import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface GradientButtonProps extends ButtonProps {
  gradient?: "primary" | "accent" | "success" | "warning";
}

const GradientButton = forwardRef<HTMLButtonElement, GradientButtonProps>(
  ({ className, gradient = "primary", ...props }, ref) => {
    const gradientClasses = {
      primary: "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70",
      accent: "bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70",
      success: "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",
      warning: "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700",
    };

    return (
      <Button
        className={cn(
          "text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300",
          gradientClasses[gradient],
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

GradientButton.displayName = "GradientButton";

export { GradientButton };
