import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { ThemeProvider } from "next-themes";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Outlier Video - Find Viral YouTube Content Instantly",
  description: "Spot outlier videos instantly, remix them into winning ideas, titles & thumbnails, and track competitors—all in one place. The ultimate tool for YouTube creators.",
  keywords: ["YouTube", "video analytics", "content creation", "viral videos", "thumbnails", "titles", "competitor tracking"],
  authors: [{ name: "Outlier Video" }],
  creator: "Outlier Video",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://outlier-video.com",
    title: "Outlier Video - Find Viral YouTube Content Instantly",
    description: "Spot outlier videos instantly, remix them into winning ideas, titles & thumbnails, and track competitors—all in one place.",
    siteName: "Outlier Video",
  },
  twitter: {
    card: "summary_large_image",
    title: "Outlier Video - Find Viral YouTube Content Instantly",
    description: "Spot outlier videos instantly, remix them into winning ideas, titles & thumbnails, and track competitors—all in one place.",
    creator: "@outliervideo",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <div className="relative flex min-h-screen flex-col">
            <Header />
            <main className="flex-1">{children}</main>
            <Footer />
          </div>
          <Analytics />
          <SpeedInsights />
        </ThemeProvider>
      </body>
    </html>
  );
}
